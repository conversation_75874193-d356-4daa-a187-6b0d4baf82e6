<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.StockFundExRightsCheckMapper">

    <!-- 分页查询股票和开基除权价格检查数据 -->
    <select id="selectPageInfo" resultType="cn.sdata.om.al.audit.vo.StockFundExRightsCheckVO">
        SELECT
            c.id,
            c.data_date,
            c.product_id,
            c.product_name,
            c.security_type,
            c.security_type_name,
            c.security_name,
            c.security_code,
            c.security_market,
            c.security_market_name,
            c.security_internal_code,
            c.registration_date,
            c.ex_rights_date,
            c.bonus_share_payment_date,
            c.dividend_payment_date,
            c.bonus_share_ratio,
            c.cash_dividend_ratio,
            c.ex_rights_flow_check,
            c.bonus_share_flow_check,
            c.dividend_flow_check,
            c.ex_rights_flow_exists,
            c.bonus_share_flow_exists,
            c.dividend_flow_exists,
            c.create_time,
            c.update_time
        FROM audit_stock_fund_ex_rights_check c
        <if test="query.valuationTime != null and query.valuationTime != ''">
            LEFT JOIN account_information ai ON c.product_id = ai.id
        </if>
        <where>
            <if test="query.dataDate != null and query.dataDate != ''">
                AND c.data_date = #{query.dataDate}
            </if>
            <if test="query.dataDateStart != null and query.dataDateStart != ''">
                AND c.data_date >= #{query.dataDateStart}
            </if>
            <if test="query.dataDateEnd != null and query.dataDateEnd != ''">
                AND c.data_date &lt;= #{query.dataDateEnd}
            </if>
            <if test="query.productId != null and query.productId != ''">
                AND c.product_id = #{query.productId}
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND c.product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.productIds != null and query.productIds.size() > 0">
                AND c.product_id IN
                <foreach collection="query.productIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.valuationTime != null and query.valuationTime != ''">
                AND ai.valuation_time = #{query.valuationTime}
            </if>
            <if test="query.securityType != null and query.securityType != ''">
                AND c.security_type = #{query.securityType}
            </if>
            <if test="query.securityTypes != null and query.securityTypes.size() > 0">
                AND c.security_type IN
                <foreach collection="query.securityTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.securityName != null and query.securityName != ''">
                AND c.security_name LIKE CONCAT('%', #{query.securityName}, '%')
            </if>
            <if test="query.securityCode != null and query.securityCode != ''">
                AND c.security_code LIKE CONCAT('%', #{query.securityCode}, '%')
            </if>
            <if test="query.securityCodes != null and query.securityCodes.size() > 0">
                AND c.security_code IN
                <foreach collection="query.securityCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.exRightsDate != null and query.exRightsDate != ''">
                AND c.ex_rights_date = #{query.exRightsDate}
            </if>
            <if test="query.bonusSharePaymentDate != null and query.bonusSharePaymentDate != ''">
                AND c.bonus_share_payment_date = #{query.bonusSharePaymentDate}
            </if>
            <if test="query.dividendPaymentDate != null and query.dividendPaymentDate != ''">
                AND c.dividend_payment_date = #{query.dividendPaymentDate}
            </if>
            <if test="query.exRightsFlowCheck != null">
                AND c.ex_rights_flow_check = #{query.exRightsFlowCheck}
            </if>
            <if test="query.bonusShareFlowCheck != null">
                AND c.bonus_share_flow_check = #{query.bonusShareFlowCheck}
            </if>
            <if test="query.dividendFlowCheck != null">
                AND c.dividend_flow_check = #{query.dividendFlowCheck}
            </if>
        </where>
        ORDER BY
            -- 异常状态排在前面：1-流水日期异常，2-业务流水缺失，0-正常，NULL-未检查
            CASE
                WHEN c.ex_rights_flow_check IN (1, 2) OR c.bonus_share_flow_check IN (1, 2) OR c.dividend_flow_check IN (1, 2) THEN 0
                WHEN c.ex_rights_flow_check = 0 AND c.bonus_share_flow_check = 0 AND c.dividend_flow_check = 0 THEN 1
                ELSE 2
            END ASC,
            c.data_date DESC,
            c.create_time DESC
    </select>

    <!-- 根据数据日期删除数据 -->
    <delete id="deleteByDataDate">
        DELETE FROM audit_stock_fund_ex_rights_check WHERE data_date = #{dataDate}
    </delete>

    <!-- 批量插入数据 -->
    <insert id="batchInsert">
        INSERT INTO audit_stock_fund_ex_rights_check (
            id, data_date, product_id, product_name, security_type, security_type_name,
            security_name, security_code, security_market, security_market_name, security_internal_code,
            registration_date, ex_rights_date, bonus_share_payment_date, dividend_payment_date,
            bonus_share_ratio, cash_dividend_ratio, ex_rights_flow_check, bonus_share_flow_check,
            dividend_flow_check, ex_rights_flow_exists, bonus_share_flow_exists, dividend_flow_exists,
            create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.dataDate}, #{item.productId}, #{item.productName},
                #{item.securityType}, #{item.securityTypeName}, #{item.securityName}, #{item.securityCode},
                #{item.securityMarket}, #{item.securityMarketName}, #{item.securityInternalCode},
                #{item.registrationDate}, #{item.exRightsDate}, #{item.bonusSharePaymentDate},
                #{item.dividendPaymentDate}, #{item.bonusShareRatio}, #{item.cashDividendRatio},
                #{item.exRightsFlowCheck}, #{item.bonusShareFlowCheck}, #{item.dividendFlowCheck},
                #{item.exRightsFlowExists}, #{item.bonusShareFlowExists}, #{item.dividendFlowExists},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 获取汇总统计数据 -->
    <select id="getSummaryStatistics" resultType="java.lang.Long">
        SELECT
            COUNT(CASE WHEN c.ex_rights_flow_check IN (1, 2)
                       OR c.bonus_share_flow_check IN (1, 2)
                       OR c.dividend_flow_check IN (1, 2) THEN 1 END) AS totalAbnormalCount
        FROM audit_stock_fund_ex_rights_check c
        <if test="valuationTime != null and valuationTime != ''">
            LEFT JOIN account_information ai ON c.product_id = ai.id
        </if>
        <where>
            <if test="startDate != null and startDate != ''">
                AND c.data_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND c.data_date &lt;= #{endDate}
            </if>
            <if test="valuationTime != null and valuationTime != ''">
                AND ai.valuation_time = #{valuationTime}
            </if>
        </where>
    </select>

    <!-- 获取异常数据统计 -->
    <select id="getAbnormalStatistics" resultType="java.util.Map">
        SELECT
            COUNT(CASE WHEN ex_rights_flow_check IN (1, 2) THEN 1 END) AS ex_rights_abnormal_total,
            COUNT(CASE WHEN bonus_share_flow_check IN (1, 2) THEN 1 END) AS bonus_share_abnormal_total,
            COUNT(CASE WHEN dividend_flow_check IN (1, 2) THEN 1 END) AS dividend_abnormal_total,
            COUNT(CASE WHEN ex_rights_flow_check IN (1, 2)
                       OR bonus_share_flow_check IN (1, 2)
                       OR dividend_flow_check IN (1, 2) THEN 1 END) AS total_abnormal_count
        FROM audit_stock_fund_ex_rights_check
        WHERE data_date = #{dataDate}
    </select>

    <!-- 获取账套ID和名称映射 -->
    <select id="getProductIds" resultType="cn.sdata.om.al.entity.CommonEntity">
        SELECT DISTINCT
            product_id as id,
            product_name as name
        FROM audit_stock_fund_ex_rights_check
        WHERE product_id IS NOT NULL AND product_name IS NOT NULL
        ORDER BY product_id
    </select>

    <!-- 根据账套ID获取证券类型列表 -->
    <select id="getSecurityTypesByProductIds" resultType="cn.sdata.om.al.entity.CommonEntity">
        SELECT DISTINCT
            security_type as id,
            security_type_name as name
        FROM audit_stock_fund_ex_rights_check
        WHERE security_type IS NOT NULL AND security_type_name IS NOT NULL
        <if test="productIds != null and productIds.size() > 0">
            AND product_id IN
            <foreach collection="productIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY security_type
    </select>

    <!-- 根据账套ID和证券类型获取证券代码列表 -->
    <select id="getSecurityCodesByConditions" resultType="cn.sdata.om.al.entity.CommonEntity">
        SELECT DISTINCT
            security_code as id,
            security_name as name
        FROM audit_stock_fund_ex_rights_check
        WHERE security_code IS NOT NULL AND security_name IS NOT NULL
        <if test="productIds != null and productIds.size() > 0">
            AND product_id IN
            <foreach collection="productIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="securityTypes != null and securityTypes.size() > 0">
            AND security_type IN
            <foreach collection="securityTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY security_code
    </select>

</mapper>
