package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.audit.entity.ScopeOfSecuritiesType;
import cn.sdata.om.al.audit.service.ScopeOfSecuritiesTypeService;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.mapper.AccountInformationMapper;
import cn.sdata.om.al.mapper.ValuationDBMapper;
import cn.sdata.om.al.mapper.mail.AccountSetMapper;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.MarketTradeDayService;
import cn.sdata.om.al.utils.PageUtil;
import cn.sdata.om.al.vo.MonthEndAndNonStandardVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.audit.entity.MonthEndAndNonStandard;
import cn.sdata.om.al.audit.mapper.MonthEndAndNonStandardMapper;
import cn.sdata.om.al.audit.service.MonthEndAndNonStandardService;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【month_end_and_non_standard(月末存款及非标行情检查)】的数据库操作Service实现
* @createDate 2025-07-29 17:15:20
*/
@Service
@AllArgsConstructor
public class MonthEndAndNonStandardServiceImpl extends ServiceImpl<MonthEndAndNonStandardMapper, MonthEndAndNonStandard>
    implements MonthEndAndNonStandardService{

    private ValuationDBMapper valuationDBMapper;

    private AccountInformationService accountInformationService;

    private final MarketTradeDayService marketTradeDayService;

    private ScopeOfSecuritiesTypeService securitiesTypeService;

    private static final HashMap<String,String> SECURITY_TYPE_MAPPING = new HashMap<>();
    static {
        SECURITY_TYPE_MAPPING.put("存款", "1");
        SECURITY_TYPE_MAPPING.put("债权投资计划", "15");
        SECURITY_TYPE_MAPPING.put("股权投资计划", "16");
        SECURITY_TYPE_MAPPING.put("信托计划", "6");
        SECURITY_TYPE_MAPPING.put("资产支持计划", "11");
    }
    @Override
    public Page<MonthEndAndNonStandardVO> selectPageInfo(MonthEndAndNonStandardVO vo) {
        if (StrUtil.isBlank(vo.getDataDate())){
            String record = baseMapper.seleDataDate();
            if (ObjectUtil.isNull(record)){
                return new Page<>(1L, 20L);
            }
            String result = record.substring(0, 7);
            vo.setDataDate(result);
        }else {
            vo.setDataDate(vo.getDataDate().substring(0, 7));
        }
        List<MonthEndAndNonStandardVO> infoList = this.baseMapper.selectInfo(vo);
        //证券类型范围 lowLimit upLimit
        List<ScopeOfSecuritiesType> list = securitiesTypeService.list();
        if (CollUtil.isEmpty(list)){
            return PageUtil.listToPage(infoList, Math.toIntExact(vo.getCurrent()), Math.toIntExact(vo.getSize()));
        }
        List<MonthEndAndNonStandardVO> result = new ArrayList<>();
        Map<String, ScopeOfSecuritiesType> rangeMap = list.stream()
                .collect(Collectors.toMap(ScopeOfSecuritiesType::getSecType, Function.identity()));
        for (MonthEndAndNonStandardVO record : infoList) {
            String securityType = record.getSecurityType();
            if (rangeMap.containsKey(securityType)){
                ScopeOfSecuritiesType scopeOfSecuritiesType = rangeMap.get(securityType);
                BigDecimal gzLowLimit = scopeOfSecuritiesType.getGzLowLimit();
                BigDecimal gzUpLimit = scopeOfSecuritiesType.getGzUpLimit();
                //比例异常提示
                String roi = record.getRoi();
                if (StrUtil.isNotBlank(roi)){
                    BigDecimal roiBigDecimal = new BigDecimal(roi);
                    if (roiBigDecimal.compareTo(gzLowLimit) <= 0 || roiBigDecimal.compareTo(gzUpLimit) >= 0){
                        record.setProportionPrompt(1);
                    }else{
                        record.setProportionPrompt(0);
                    }
                }
                BigDecimal jgLowLimit = scopeOfSecuritiesType.getJgLowLimit();
                BigDecimal jgUpLimit = scopeOfSecuritiesType.getJgUpLimit();
                String priceVolatility = record.getPriceVolatility();
                if (StrUtil.isNotBlank(priceVolatility)){
                    BigDecimal priceVolatilityBigDecimal = new BigDecimal(priceVolatility);
                    if (priceVolatilityBigDecimal.compareTo(jgLowLimit) <= 0 || priceVolatilityBigDecimal.compareTo(jgUpLimit) >= 0){
                        record.setVolatilityAlert(1);
                    }else{
                        record.setVolatilityAlert(0);
                    }
                }
            }
            boolean flag = true;
            if (vo.getProportionPrompt() != null){
                flag = vo.getProportionPrompt().equals(record.getProportionPrompt());
            }
            if (vo.getVolatilityAlert() != null){
                flag = vo.getVolatilityAlert().equals(record.getVolatilityAlert());
            }
            if (flag){
                result.add(record);
            }
        }

        return PageUtil.listToPage(result, Math.toIntExact(vo.getCurrent()), Math.toIntExact(vo.getSize()));
    }




    @Override
    public Page<MonthEndAndNonStandardVO> selectPageInfoV2(MonthEndAndNonStandardVO vo) {
        if (StrUtil.isBlank(vo.getDataDate())){
            String record = valuationDBMapper.seleDataDate();
            if (ObjectUtil.isNull(record)){
                return new Page<>(1L, 20L);
            }
            String result = record.substring(0, 7);
            vo.setDataDate(result);
        }else {
            vo.setDataDate(vo.getDataDate().substring(0, 7));
        }

        List<AccountInformation> accountList = accountInformationService.list();
        Map<String, AccountInformation> accountInformationMap = accountList.stream()
                .collect(Collectors.toMap(AccountInformation::getId, a -> a, (a, b) -> a));

        if (CollUtil.isNotEmpty(vo.getProductNames())){
            List<String> productIds = new ArrayList<>();
            Map<String, AccountInformation> informationMap = accountList.stream()
                    .collect(Collectors.toMap(AccountInformation::getFullProductName, x -> x, (a, b) -> a));
            for (String productName : vo.getProductNames()) {
                if (informationMap.containsKey(productName)){
                    String id = informationMap.get(productName).getId();
                    productIds.add(id);
                }
            }
            vo.setProductNames(productIds);
        }

        if (CollUtil.isNotEmpty(vo.getSecurityTypes())){
            List<String> securityTypes = new ArrayList<>();
            for (String securityType : vo.getSecurityTypes()) {
                securityTypes.add(SECURITY_TYPE_MAPPING.get(securityType));
            }
            vo.setSecurityTypes(securityTypes);
        }


        // 解析字符串为 YearMonth
        YearMonth yearMonth = YearMonth.parse(vo.getDataDate(), DateTimeFormatter.ofPattern("yyyy-MM"));

        // 获取该月的最后一天
        LocalDate lastDay = yearMonth.atEndOfMonth();
        //该月上一交易日
        String thisMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDay.toString());
//        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
        //todo 根据数据日期获取本月末估值价格
        vo.setDataDate(lastDay.toString());
        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        if (CollUtil.isEmpty(thisMonth)){
            vo.setDataDate(thisMonthTradeDay);
            thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        }



        // 上个月的最后一天
        LocalDate lastDayPrevMonth = yearMonth.minusMonths(1).atEndOfMonth();

        //该月上一交易日
        String lastDayPrevMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDayPrevMonth.toString());
        //todo 根据数据日期获取上月末估值价格
        vo.setDataDate(lastDayPrevMonth.toString());
        List<MonthEndAndNonStandard> lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        if (CollUtil.isEmpty(lastMonth)){
            vo.setDataDate(lastDayPrevMonthTradeDay);
            lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        }
        Map<String, MonthEndAndNonStandard> lastMonthMap = lastMonth.stream()
                .collect(Collectors.toMap(m -> m.getProductId() +"-"+ m.getSecurityCode(),Function.identity()));

        List<MonthEndAndNonStandardVO> infoList = new ArrayList<>();
        for (MonthEndAndNonStandard record : thisMonth) {


            String securityMarketValue = StrUtil.isBlank(record.getSecurityMarketValue()) ? "0" : record.getSecurityMarketValue();
            String securityCost = record.getSecurityCost();
            //估值与本金的比例 （估增=市值-成本）/成本
            if (StrUtil.isBlank(securityCost) || "0".equals(securityCost)){
                record.setRoi("0");
            }else {
                BigDecimal roi = new BigDecimal(securityMarketValue).min(new BigDecimal(securityCost))
                        .divide(new BigDecimal(securityCost), 10, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                record.setRoi(roi.toPlainString());
            }


            MonthEndAndNonStandard lastMonthInfo = lastMonthMap.get(record.getProductId()+"-"+record.getSecurityCode());
            //本月末估值价格
            String monthEndValuationPrice = StrUtil.isBlank(record.getMonthEndValuationPrice()) ? "0" : record.getMonthEndValuationPrice();

            MonthEndAndNonStandardVO recordVO = new MonthEndAndNonStandardVO();
            BeanUtil.copyProperties(record, recordVO);
            AccountInformation accountInformation = accountInformationMap.get(record.getProductId());
            if (accountInformation != null){
                recordVO.setProductName(accountInformation.getFullProductName());
                recordVO.setProductCode(accountInformation.getProductCode());
            }
            infoList.add(recordVO);
            if (Objects.isNull(lastMonthInfo)){
                continue;
            }

            //上月末估值价格
            String valuationPriceEndLastMonth = lastMonthInfo.getMonthEndValuationPrice();
            record.setValuationPriceEndLastMonth(valuationPriceEndLastMonth);
            if (StrUtil.isBlank(valuationPriceEndLastMonth) || "0".equals(valuationPriceEndLastMonth)){
                record.setPriceVolatility("0");
                continue;
            }
            //价格波动率 （本月-上月）/上月
            BigDecimal priceVolatility = new BigDecimal(monthEndValuationPrice).min(new BigDecimal(valuationPriceEndLastMonth)
                            .divide(new BigDecimal(valuationPriceEndLastMonth), 10, RoundingMode.HALF_UP))
                    .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            record.setPriceVolatility(priceVolatility.toPlainString());



        }


        //证券类型范围 lowLimit upLimit
        List<ScopeOfSecuritiesType> list = securitiesTypeService.list();
        if (CollUtil.isEmpty(list)){
            return PageUtil.listToPage(infoList, Math.toIntExact(vo.getCurrent()), Math.toIntExact(vo.getSize()));
        }
        List<MonthEndAndNonStandardVO> result = new ArrayList<>();
        Map<String, ScopeOfSecuritiesType> rangeMap = list.stream()
                .collect(Collectors.toMap(ScopeOfSecuritiesType::getSecType, Function.identity()));
        for (MonthEndAndNonStandardVO record : infoList) {
            String securityType = record.getSecurityType();
            if (rangeMap.containsKey(securityType)){
                ScopeOfSecuritiesType scopeOfSecuritiesType = rangeMap.get(securityType);
                BigDecimal gzLowLimit = scopeOfSecuritiesType.getGzLowLimit();
                BigDecimal gzUpLimit = scopeOfSecuritiesType.getGzUpLimit();
                //比例异常提示
                String roi = record.getRoi();
                if (StrUtil.isNotBlank(roi)){
                    BigDecimal roiBigDecimal = new BigDecimal(roi);
                    if (roiBigDecimal.compareTo(gzLowLimit) <= 0 || roiBigDecimal.compareTo(gzUpLimit) >= 0){
                        record.setProportionPrompt(1);
                    }else{
                        record.setProportionPrompt(0);
                    }
                }
                BigDecimal jgLowLimit = scopeOfSecuritiesType.getJgLowLimit();
                BigDecimal jgUpLimit = scopeOfSecuritiesType.getJgUpLimit();
                String priceVolatility = record.getPriceVolatility();
                if (StrUtil.isNotBlank(priceVolatility)){
                    BigDecimal priceVolatilityBigDecimal = new BigDecimal(priceVolatility);
                    if (priceVolatilityBigDecimal.compareTo(jgLowLimit) <= 0 || priceVolatilityBigDecimal.compareTo(jgUpLimit) >= 0){
                        record.setVolatilityAlert(1);
                    }else{
                        record.setVolatilityAlert(0);
                    }
                }
            }
            boolean flag = true;
            if (vo.getProportionPrompt() != null){
                flag = vo.getProportionPrompt().equals(record.getProportionPrompt());
            }
            if (vo.getVolatilityAlert() != null){
                flag = vo.getVolatilityAlert().equals(record.getVolatilityAlert());
            }
            if (flag){
                result.add(record);
            }
        }

        return PageUtil.listToPage(result, Math.toIntExact(vo.getCurrent()), Math.toIntExact(vo.getSize()));
    }


    @Override
    public List<MonthEndAndNonStandardVO> startExport(MonthEndAndNonStandardVO vo) {
        if (StrUtil.isBlank(vo.getDataDate())){
            String record = baseMapper.seleDataDate();
            if (ObjectUtil.isNull(record)){
                return new ArrayList<>();
            }
            String result = record.substring(0, 7);
            vo.setDataDate(result);
        }else {
            vo.setDataDate(vo.getDataDate().substring(0, 7));
        }

        List<MonthEndAndNonStandardVO> infoList = this.baseMapper.selectInfo(vo);
        //证券类型范围 lowLimit upLimit
        List<ScopeOfSecuritiesType> list = securitiesTypeService.list();
        if (CollUtil.isEmpty(list)){
            return infoList;
        }
        List<MonthEndAndNonStandardVO> result = new ArrayList<>();
        Map<String, ScopeOfSecuritiesType> rangeMap = list.stream()
                .collect(Collectors.toMap(ScopeOfSecuritiesType::getSecType, Function.identity()));
        for (MonthEndAndNonStandardVO record : infoList) {
            String securityType = record.getSecurityType();
            if (rangeMap.containsKey(securityType)){
                ScopeOfSecuritiesType scopeOfSecuritiesType = rangeMap.get(securityType);
                BigDecimal gzLowLimit = scopeOfSecuritiesType.getGzLowLimit();
                BigDecimal gzUpLimit = scopeOfSecuritiesType.getGzUpLimit();
                //比例异常提示
                String roi = record.getRoi();
                if (StrUtil.isNotBlank(roi)){
                    BigDecimal roiBigDecimal = new BigDecimal(roi);
                    if (roiBigDecimal.compareTo(gzLowLimit) <= 0 || roiBigDecimal.compareTo(gzUpLimit) >= 0){
                        record.setProportionPrompt(1);
                    }else{
                        record.setProportionPrompt(0);
                    }
                }
                BigDecimal jgLowLimit = scopeOfSecuritiesType.getJgLowLimit();
                BigDecimal jgUpLimit = scopeOfSecuritiesType.getJgUpLimit();
                String priceVolatility = record.getPriceVolatility();
                if (StrUtil.isNotBlank(priceVolatility)){
                    BigDecimal priceVolatilityBigDecimal = new BigDecimal(priceVolatility);
                    if (priceVolatilityBigDecimal.compareTo(jgLowLimit) <= 0 || priceVolatilityBigDecimal.compareTo(jgUpLimit) >= 0){
                        record.setVolatilityAlert(1);
                    }else{
                        record.setVolatilityAlert(0);
                    }
                }
            }
            boolean flag = true;
            if (vo.getProportionPrompt() != null){
                flag = vo.getProportionPrompt().equals(record.getProportionPrompt());
            }
            if (vo.getVolatilityAlert() != null){
                flag = vo.getVolatilityAlert().equals(record.getVolatilityAlert());
            }
            if (flag){
                result.add(record);
            }
        }

        return result;
    }

    @Override
    public List<String> secListInfo(String securityType,String securityCode,String dataDate) {
        if (StrUtil.isBlank(dataDate)){
            String record = valuationDBMapper.seleDataDate();
            if (ObjectUtil.isNull(record)){
                return new ArrayList<>();
            }
            dataDate = record.substring(0, 7);
        }else {
            dataDate = dataDate.substring(0, 7);
        }
        MonthEndAndNonStandardVO vo = new MonthEndAndNonStandardVO();
        vo.setDataDate(dataDate);
        List<MonthEndAndNonStandard> list = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        // 解析字符串为 YearMonth
        YearMonth yearMonth = YearMonth.parse(vo.getDataDate(), DateTimeFormatter.ofPattern("yyyy-MM"));
        // 获取该月的最后一天
        LocalDate lastDay = yearMonth.atEndOfMonth();
        //该月上一交易日
        String thisMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDay.toString());
//        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
        //根据数据日期获取本月末估值价格
        vo.setDataDate(lastDay.toString());
        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        if (CollUtil.isEmpty(thisMonth)){
            vo.setDataDate(thisMonthTradeDay);
            thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        }
        if (CollUtil.isEmpty(thisMonth)){
            return new ArrayList<>();
        }
        return thisMonth.stream().map(MonthEndAndNonStandard::getSecurityCode).distinct().collect(Collectors.toList());
    }

    @Override
    public void syncMonthEndAndNonStandardInfo(String today) {
//
//        //估值系统中的证券持仓表（可以取到账套下的账套持仓、类型、市场） 账套对应 证券类型/证券名称/证券代码
//        List<MonthEndAndNonStandard> prodSecurityList = valuationDBMapper.selectProdSecurityList("","");
//        Map<String, MonthEndAndNonStandard> prodSecurityMap = prodSecurityList.stream()
//                .collect(Collectors.toMap(MonthEndAndNonStandard::getVcScdm, Function.identity()));
//
//        // 解析字符串为 LocalDate
//        LocalDate date = LocalDate.parse(today, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//
//        // 获取该月的最后一天
//        LocalDate lastDay = date.withDayOfMonth(date.lengthOfMonth());
//        //该月上一交易日
//        String thisMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDay.toString());
////        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
//        //todo 根据数据日期获取本月末估值价格
//        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(lastDay.toString());
//        if (CollUtil.isEmpty(thisMonth)){
//            thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(thisMonthTradeDay);
//        }
//
//
//        // 转 LocalDate
//        LocalDate lastMonthDay = LocalDate.parse(today, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        // 上个月最后一天
//        LocalDate lastDayPrevMonth = lastMonthDay.minusMonths(1).withDayOfMonth(
//                date.minusMonths(1).lengthOfMonth()
//        );
//        //该月上一交易日
//        String lastDayPrevMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDayPrevMonth.toString());
//        //todo 根据数据日期获取上月末估值价格
//        List<MonthEndAndNonStandard> lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(lastDayPrevMonth.toString());
//        if (CollUtil.isEmpty(lastMonth)){
//            lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(lastDayPrevMonthTradeDay);
//        }
//        Map<String, MonthEndAndNonStandard> lastMonthMap = lastMonth.stream()
//                .collect(Collectors.toMap(MonthEndAndNonStandard::getProductId, Function.identity()));
//
//
//        for (MonthEndAndNonStandard record : thisMonth) {
//
//            //账套对应 证券类型/证券名称/证券代码
//            MonthEndAndNonStandard monthEndAndNonStandard = prodSecurityMap.get(record.getVcScdm());
//            if (ObjectUtil.isNotNull(monthEndAndNonStandard)){
//                record.setSecurityType(monthEndAndNonStandard.getSecurityType());
//                record.setSecurityName(monthEndAndNonStandard.getSecurityName());
//                record.setSecurityCode(monthEndAndNonStandard.getSecurityCode());
//            }
//
//            String securityMarketValue = StrUtil.isBlank(record.getSecurityMarketValue()) ? "0" : record.getSecurityMarketValue();
//            String securityCost = record.getSecurityCost();
//            //估值与本金的比例 （估增=市值-成本）/成本
//            if (StrUtil.isBlank(securityCost) || "0".equals(securityCost)){
//                record.setRoi("0");
//            }else {
//                BigDecimal roi = new BigDecimal(securityMarketValue).min(new BigDecimal(securityCost))
//                        .divide(new BigDecimal(securityCost), 10, RoundingMode.HALF_UP)
//                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
//                record.setRoi(roi.toPlainString());
//            }
//
//
//            MonthEndAndNonStandard lastMonthInfo = lastMonthMap.get(record.getProductId());
//            //本月末估值价格
//            String monthEndValuationPrice = StrUtil.isBlank(record.getMonthEndValuationPrice()) ? "0" : record.getMonthEndValuationPrice();
//
//            if (Objects.isNull(lastMonthInfo)){
//                continue;
//            }
//
//            //上月末估值价格
//            String valuationPriceEndLastMonth = lastMonthInfo.getMonthEndValuationPrice();
//            record.setValuationPriceEndLastMonth(valuationPriceEndLastMonth);
//            if (StrUtil.isBlank(valuationPriceEndLastMonth) || "0".equals(valuationPriceEndLastMonth)){
//                record.setPriceVolatility("0");
//                continue;
//            }
//            //价格波动率 （本月-上月）/上月
//            BigDecimal priceVolatility = new BigDecimal(monthEndValuationPrice).min(new BigDecimal(valuationPriceEndLastMonth)
//                    .divide(new BigDecimal(valuationPriceEndLastMonth), 10, RoundingMode.HALF_UP))
//                    .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
//            record.setPriceVolatility(priceVolatility.toPlainString());
//        }
//
//        if (CollUtil.isNotEmpty(thisMonth)){
//            this.saveOrUpdateBatch(thisMonth);
//        }

    }

}




