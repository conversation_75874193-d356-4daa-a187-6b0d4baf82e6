package cn.sdata.om.al.audit.vo;

import cn.sdata.om.al.audit.enums.FlowCheckStatus;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 股票和开基除权价格检查VO
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class StockFundExRightsCheckVO {

    /**
     * ID
     */
    private String id;

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 账套编号
     */
    private String productId;

    /**
     * 账套名称
     */
    private String productName;

    /**
     * 证券类型
     */
    private String securityType;

    /**
     * 证券类型名称
     */
    private String securityTypeName;

    /**
     * 证券名称
     */
    private String securityName;

    /**
     * 证券代码
     */
    private String securityCode;

    /**
     * 交易市场
     */
    private String securityMarket;

    /**
     * 交易市场名称
     */
    private String securityMarketName;

    /**
     * 证券内部代码
     */
    private String securityInternalCode;

    /**
     * 登记日
     */
    private String registrationDate;

    /**
     * 除权日
     */
    private String exRightsDate;

    /**
     * 红股发放日
     */
    private String bonusSharePaymentDate;

    /**
     * 红利发放日
     */
    private String dividendPaymentDate;

    /**
     * 红股分红比例
     */
    private BigDecimal bonusShareRatio;

    /**
     * 现金分红比例
     */
    private BigDecimal cashDividendRatio;

    /**
     * 除权流水检查结果
     */
    private FlowCheckStatus exRightsFlowCheck;

    /**
     * 红股流水检查结果
     */
    private FlowCheckStatus bonusShareFlowCheck;

    /**
     * 红利发放流水检查结果
     */
    private FlowCheckStatus dividendFlowCheck;

    /**
     * 除权流水是否存在
     */
    private Boolean exRightsFlowExists;

    /**
     * 红股流水是否存在
     */
    private Boolean bonusShareFlowExists;

    /**
     * 红利发放流水是否存在
     */
    private Boolean dividendFlowExists;
}

