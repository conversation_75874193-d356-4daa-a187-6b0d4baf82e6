package cn.sdata.om.al.audit.controller;


import cn.hutool.core.collection.CollUtil;
import cn.sdata.om.al.audit.dto.PortfolioFluctuationRangeDTO;
import cn.sdata.om.al.audit.entity.MonthEndAndNonStandard;
import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.audit.entity.ScopeOfSecuritiesType;
import cn.sdata.om.al.audit.enums.WarningStatus;
import cn.sdata.om.al.audit.service.MonthEndAndNonStandardService;
import cn.sdata.om.al.audit.service.ScopeOfSecuritiesTypeService;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.utils.ExcelUtil;
import cn.sdata.om.al.vo.MonthEndAndNonStandardVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 月末存款及非标行情检查
 */
@RestController
@RequestMapping("/audit/month-end-and-non-standard")
@AllArgsConstructor
public class MonthEndAndNonStandardController {

    private MonthEndAndNonStandardService monthEndAndNonStandardService;
    private ScopeOfSecuritiesTypeService securitiesTypeService;

    /**
     * 月末存款及非标行情检查
     */
    @PostMapping("page")
    public R<Page<MonthEndAndNonStandardVO>> page(@RequestBody MonthEndAndNonStandardVO commonPageParam) {
        Page<MonthEndAndNonStandardVO> page = monthEndAndNonStandardService.selectPageInfoV2(commonPageParam);
        return R.ok(page);
    }

    /**
     * 月末存款及非标行情检查统计
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @return 回撤和波动数量统计
     */
    @GetMapping("/count")
    public R<Long> count(@RequestParam(value = "startDate", required = false) String startDate,
                                      @RequestParam(value = "endDate" , required = false)String endDate){
        LambdaQueryWrapper<MonthEndAndNonStandard> queryWrapper = new LambdaQueryWrapper<>();
        startDate = startDate.substring(0, 7);
        queryWrapper.likeRight(MonthEndAndNonStandard::getDataDate, startDate);
        long count = monthEndAndNonStandardService.count(queryWrapper);
        return R.ok(count);
    }

    /**
     * 导出列表信息
     */
    @PostMapping("/startExport")
    public void startExport(@RequestBody MonthEndAndNonStandardVO commonPageParam, HttpServletResponse response) {
        List<MonthEndAndNonStandardVO> list = monthEndAndNonStandardService.startExport(commonPageParam);
        ExcelUtil.exportExcel(response,list, "月末存款及非标行情检查.xlsx", MonthEndAndNonStandardVO.class, true);
    }

    /**
     * 证券信息列表
     * @return 波动范围规则列表
     */
    @GetMapping("/sec-list")
    public R<List<String>> secListInfo(
            @RequestParam(required = false,name = "securityType") String securityType,
            @RequestParam(required = false,value = "securityCode") String securityCode,
            @RequestParam(required = false,value = "dataDate") String dataDate) {
        return R.ok(monthEndAndNonStandardService.secListInfo(securityType,securityCode,dataDate));
    }
    /**
     * 获取所有波动范围规则
     * @return 波动范围规则列表
     */
    @GetMapping("/list")
    public R<List<ScopeOfSecuritiesType>> listAllRanges() {
        List<ScopeOfSecuritiesType> ranges = securitiesTypeService.list();
        String[] split = "存款、债权投资计划、股权投资计划、信托计划、资产支持计划".split("、");
        Map<String, ScopeOfSecuritiesType> collect = new HashMap<>();
        if (CollUtil.isNotEmpty(ranges)){
            collect = ranges.stream().collect(Collectors.toMap(ScopeOfSecuritiesType::getSecType, Function.identity()));
        }
        List<String> arrayList = Arrays.asList(split);
        List<ScopeOfSecuritiesType> result = new ArrayList<>();
        for (String s : arrayList) {
            if (collect.containsKey(s)){
                result.add(collect.get(s));
            }else {
                ScopeOfSecuritiesType scopeOfSecuritiesType = new ScopeOfSecuritiesType();
                scopeOfSecuritiesType.setSecType(s);
                result.add(scopeOfSecuritiesType);
            }
        }
        return R.ok(result);
    }

    /**
     * 保存单个波动范围规则
     * @return 是否保存成功
     */
    @PostMapping("/save")
    public R<Boolean> saveFluctuationRange(@RequestBody ScopeOfSecuritiesType scope) {
        LambdaQueryWrapper<ScopeOfSecuritiesType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScopeOfSecuritiesType::getSecType, scope.getSecType());
        securitiesTypeService.remove(queryWrapper);
        scope.setUpdateTime(new Date());
        boolean result = securitiesTypeService.save(scope);
        return R.ok(result);
    }


}
