package cn.sdata.om.al.audit.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.audit.entity.PortfolioFluctuationRange;
import cn.sdata.om.al.audit.entity.PortfolioNetValueFluctuation;
import cn.sdata.om.al.audit.util.CompareUtil;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.enums.ValuationTime;
import cn.sdata.om.al.job.audit.PortfolioNetValueFluctuationMailJob;
import cn.sdata.om.al.job.audit.PortfolioNetValueRetracementMailJob;
import cn.sdata.om.al.mapper.ValuationDBMapper;
import cn.sdata.om.al.mapper.audit.PortfolioNetValueFluctuationMapper;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.MarketTradeDayService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.*;
import static cn.sdata.om.al.qrtz.constant.CronConstant.SYNC;

@Service
@RequiredArgsConstructor
@Slf4j
public class PortfolioNetValueFluctuationService extends ServiceImpl<PortfolioNetValueFluctuationMapper, PortfolioNetValueFluctuation> {

    private final CronService cronService;
    private final MarketTradeDayService marketTradeDayService;
    private final ValuationDBMapper valuationDBMapper;
    private final AccountInformationService accountInformationService;
    private final PortfolioFluctuationRangeService fluctuationRangeService;

    public void sendRetracementMail(List<String> productIds, String date) {
        sendMail(productIds, date, "PORTFOLIO_NET_VALUE_RETRACEMENT");
    }

    public void sendFluctuationMail(List<String> productIds, String date) {
        sendMail(productIds, date, "PORTFOLIO_NET_VALUE_FLUCTUATION");
    }

    /**
     * 公共方法：解析日期逻辑。如果date为空，15:00前取T-1，15:00后取T
     */
    private String resolveDataDate(String date) {
        if (date != null && !date.isEmpty()) {
            return date;
        }
        LocalTime currentTime = LocalTime.now();
        LocalTime cutoffTime = LocalTime.of(17, 0);
        if (currentTime.isBefore(cutoffTime)) {
            // 15:00之前：T-1
            return marketTradeDayService.getNetValueTradeDay(null, "00", -1);
        } else {
            // 15:00之后：T
            return marketTradeDayService.getNetValueTradeDay(null, "00", 0);
        }
    }

    /**
     * 公共方法：解析产品ID逻辑。如果productIds为空，则自动推断所有有回撤/波动的产品ID
     * @param productIds 产品ID列表
     * @param dataDate   数据日期
     * @param type       类型："retracement" 或 "fluctuation"
     * @return 解析后的产品ID列表
     */
    private List<String> resolveProductIds(List<String> productIds, String dataDate, String type) {
        if (productIds != null && !productIds.isEmpty()) {
            return productIds;
        }
        // 自动推断：查找指定日期所有有回撤/波动的产品ID
        LambdaQueryWrapper<PortfolioNetValueFluctuation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PortfolioNetValueFluctuation::getDataDate, dataDate);
        if ("retracement".equals(type)) {
            wrapper.eq(PortfolioNetValueFluctuation::getRetracementFlag, 1);
        } else if ("fluctuation".equals(type)) {
            wrapper.eq(PortfolioNetValueFluctuation::getAbnormalFluctuation, 1);
        }
        return this.list(wrapper).stream().map(PortfolioNetValueFluctuation::getProductId).distinct().collect(Collectors.toList());
    }

    private void sendMail(List<String> productIds, String date, String specifiedHandler) {
        String resolvedDate = resolveDataDate(date);
        String type = "PORTFOLIO_NET_VALUE_RETRACEMENT".equals(specifiedHandler) ? "retracement" : "fluctuation";
        List<String> resolvedProductIds = resolveProductIds(productIds, resolvedDate, type);
        List<String> jobIds = cronService.getJobIdByClass(PortfolioNetValueFluctuationMailJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(PRODUCT_ID, resolvedProductIds);
        jobDataMap.put(DATA_DATE, resolvedDate);
        jobDataMap.put(REMOTE_FILE, new java.util.LinkedHashMap<String, RemoteFileInfo>());
        jobDataMap.put(SYNC, true);
        jobDataMap.put("specifiedHandler", specifiedHandler);
        cronService.startJobNow(jobIds, jobDataMap);
    }

    public void sendRetracementMailV2(List<String> productIds, List<String> dates) {
        String resolvedDate = "";
        if (CollectionUtil.isNotEmpty(dates)) {
            if (dates.size() == 1 && dates.contains(DateUtil.today())) {
                LocalTime currentTime = LocalTime.now();
                LocalTime cutoffTime = LocalTime.of(17, 0);
                if (currentTime.isBefore(cutoffTime)) {
                    // 15:00之前：T-1
                    resolvedDate = marketTradeDayService.getNetValueTradeDay(null, "00", -1);
                } else {
                    // 15:00之后：T
                    resolvedDate = marketTradeDayService.getNetValueTradeDay(null, "00", 0);
                }
                dates.clear();
                dates.add(resolvedDate);
            }
        }
        List<String> resolvedProductIds = resolveRetracementProductIds(dates);
        List<String> jobIds = cronService.getJobIdByClass(PortfolioNetValueRetracementMailJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(PRODUCT_ID, resolvedProductIds);
        jobDataMap.put(DATA_DATE, resolvedDate);
        Map<String, Object> extraData = new HashMap<>();
        extraData.put("dataDateList", dates);
        jobDataMap.put("extraData", extraData);
        jobDataMap.put(REMOTE_FILE, new java.util.LinkedHashMap<String, RemoteFileInfo>());
        jobDataMap.put(SYNC, true);
        jobDataMap.put("specifiedHandler", "PORTFOLIO_NET_VALUE_RETRACEMENT");
        cronService.startJobNow(jobIds, jobDataMap);
    }

    public void sendFluctuationMailV2(List<String> productIds, List<String> dates) {
        String resolvedDate = "";
        if (CollectionUtil.isNotEmpty(dates)) {
            if (dates.size() == 1 && dates.contains(DateUtil.today())) {
                LocalTime currentTime = LocalTime.now();
                LocalTime cutoffTime = LocalTime.of(17, 0);
                if (currentTime.isBefore(cutoffTime)) {
                    // 15:00之前：T-1
                    resolvedDate = marketTradeDayService.getNetValueTradeDay(null, "00", -1);
                } else {
                    // 15:00之后：T
                    resolvedDate = marketTradeDayService.getNetValueTradeDay(null, "00", 0);
                }
                dates.clear();
                dates.add(resolvedDate);
            }
        }
        List<String> resolvedProductIds = resolveFluctuationProductIds(dates);
        List<String> jobIds = cronService.getJobIdByClass(PortfolioNetValueFluctuationMailJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(PRODUCT_ID, resolvedProductIds);
        jobDataMap.put(DATA_DATE, resolvedDate);
        Map<String, Object> extraData = new HashMap<>();
        extraData.put("dataDateList", dates);
        jobDataMap.put("extraData", extraData);
        jobDataMap.put(REMOTE_FILE, new java.util.LinkedHashMap<String, RemoteFileInfo>());
        jobDataMap.put(SYNC, true);
        jobDataMap.put("specifiedHandler", "PORTFOLIO_NET_VALUE_FLUCTUATION");
        cronService.startJobNow(jobIds, jobDataMap);
    }

    private List<String> resolveFluctuationProductIds(List<String> dates) {
        // 自动推断：查找指定日期所有有回撤/波动的产品ID
        LambdaQueryWrapper<PortfolioNetValueFluctuation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PortfolioNetValueFluctuation::getAbnormalFluctuation, 1);
        wrapper.in(PortfolioNetValueFluctuation::getDataDate, dates);
        return this.list(wrapper).stream().map(PortfolioNetValueFluctuation::getProductId).distinct().collect(Collectors.toList());
    }


    private List<String> resolveRetracementProductIds(List<String> dates) {
        // 自动推断：查找指定日期所有有回撤/波动的产品ID
        LambdaQueryWrapper<PortfolioNetValueFluctuation> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PortfolioNetValueFluctuation::getDataDate, dates);
        wrapper.eq(PortfolioNetValueFluctuation::getRetracementFlag, 1);
        return this.list(wrapper).stream().map(PortfolioNetValueFluctuation::getProductId).distinct().collect(Collectors.toList());
    }

    /**
     * 处理指定日期的净值波动与回撤逻辑（与Job一致）
     */
    public void processFluctuationForDate(String date) {
        String lastTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(date);
        // 获取所有产品信息
        List<AccountInformation> accountList = accountInformationService.list();
        Map<String, AccountInformation> accountMap = accountList.stream()
                .collect(Collectors.toMap(AccountInformation::getProductCode, a -> a, (a, b) -> a));
        List<String> productCodes = List.of();

        // 获取 today 和 lastTradeDay 的净值数据
        List<PortfolioNetValueFluctuation> todayList = valuationDBMapper.getPortfolioNetValueFluctuation(date, productCodes);
        List<PortfolioNetValueFluctuation> lastList = valuationDBMapper.getPortfolioNetValueFluctuation(lastTradeDay, productCodes);
        List<CommonEntity> list = valuationDBMapper.getProductCodeMapping();
        Map<String, String> productCodeMappingMap = list.stream().collect(Collectors.toMap(CommonEntity::getId, CommonEntity::getName, (s, s2) -> s2));
        Map<String, PortfolioNetValueFluctuation> lastMap = lastList.stream()
                .collect(Collectors.toMap(PortfolioNetValueFluctuation::getProductCode, x -> x, (a, b) -> a));

        // 查询今日已标记回撤原因的账套productId
        LambdaQueryWrapper<PortfolioNetValueFluctuation> markedWrapper = new LambdaQueryWrapper<>();
        markedWrapper.eq(PortfolioNetValueFluctuation::getDataDate, date);
        markedWrapper.isNotNull(PortfolioNetValueFluctuation::getRetracementReason);
        List<PortfolioNetValueFluctuation> markedList = this.list(markedWrapper);
        Set<String> markedProductIds = markedList.stream()
                .map(PortfolioNetValueFluctuation::getProductId)
                .collect(Collectors.toSet());
        // 过滤todayList，移除已标记的账套
        todayList = todayList.stream()
                .filter(f -> !markedProductIds.contains(f.getProductId()))
                .collect(Collectors.toList());

        // 获取波动范围规则
        List<PortfolioFluctuationRange> rangeList = fluctuationRangeService.list();
        Map<String, List<PortfolioFluctuationRange>> rangeMap = rangeList.stream()
                .collect(Collectors.groupingBy(PortfolioFluctuationRange::getProductId));

        for (PortfolioNetValueFluctuation fluctuation : todayList) {
            String productCode = fluctuation.getProductCode();
            AccountInformation account = accountMap.get(productCode);
            if (account == null && productCodeMappingMap.containsKey(productCode)) {
                String mappedProductCode = productCodeMappingMap.get(productCode);
                account = accountMap.get(mappedProductCode);
            }

            if (account == null) {
                continue;
            }
            Integer productCategory = account.getProductCategory();
            if (productCategory != 3) {
                continue;
            }
            fluctuation.setProductId(account.getId());
            fluctuation.setProductCode(productCode);
            fluctuation.setValuationTime(account.getValuationTime());
            fluctuation.setDataDate(date);

            PortfolioNetValueFluctuation last = lastMap.get(productCode);

            // 补充赋值上一日净值和万份收益
            fluctuation.setPreviousNetValue(last != null ? last.getCurrentNetValue() : null);
            fluctuation.setPreviousTenThousandIncome(last != null ? last.getCurrentTenThousandIncome() : null);

            // 1. 计算净值波动率
            BigDecimal todayValue = getValue(account, fluctuation);
            BigDecimal lastValue = getValue(account, last);
            if (todayValue != null && lastValue != null && lastValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal rate = todayValue.subtract(lastValue).multiply(new BigDecimal("100")).divide(lastValue, 6, RoundingMode.HALF_UP);
                fluctuation.setFluctuationRate(rate);
            } else {
                fluctuation.setFluctuationRate(null);
            }

            // 2. 判断净值异常波动
            boolean abnormal = false;
            List<PortfolioFluctuationRange> ranges = rangeMap.get(account.getId());
            if (ranges != null && fluctuation.getFluctuationRate() != null) {
                abnormal = ranges.stream().noneMatch(range ->
                        CompareUtil.isInRange(fluctuation.getFluctuationRate(),
                                range.getLowerLimit(), range.getLowerCompareType(),
                                range.getUpperLimit(), range.getUpperCompareType())
                );
            }
            fluctuation.setAbnormalFluctuation(abnormal ? 1 : 0);

            // 3. 判断净值回撤
            if (todayValue != null && lastValue != null) {
                fluctuation.setRetracementFlag(todayValue.subtract(lastValue).compareTo(BigDecimal.ZERO) < 0 ? 1 : 0);
            }

            // 4. 计算差值
            if (todayValue != null && lastValue != null) {
                if (account.getIsCurrency() != null && account.getIsCurrency() == 1) {
                    fluctuation.setTenThousandIncomeDifference(todayValue.subtract(lastValue));
                } else {
                    fluctuation.setNetValueDifference(todayValue.subtract(lastValue));
                }
            }

            // 存储前字段置空处理
            if (account.getIsCurrency() != null && account.getIsCurrency() == 1) {
                // 货币类，净值相关字段置空
                fluctuation.setCurrentNetValue(null);
                fluctuation.setPreviousNetValue(null);
                fluctuation.setNetValueDifference(null);
                fluctuation.setFluctuationRate(null);
            } else {
                // 非货币类，万份收益相关字段置空
                fluctuation.setCurrentTenThousandIncome(null);
                fluctuation.setPreviousTenThousandIncome(null);
                fluctuation.setTenThousandIncomeDifference(null);
            }
        }

        PortfolioNetValueFluctuationService portfolioNetValueFluctuationService = (PortfolioNetValueFluctuationService) AopContext.currentProxy();
        portfolioNetValueFluctuationService.saveAll(date, todayList);
    }

    @Transactional
    public void saveAll(String date, List<PortfolioNetValueFluctuation> todayList) {
        // 删除并保存
        LambdaQueryWrapper<PortfolioNetValueFluctuation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PortfolioNetValueFluctuation::getDataDate, date);
        this.remove(wrapper);
        this.saveBatch(todayList);
    }

    /**
     * 区间批量处理
     */
    public Map<String, String> processFluctuationForDateRange(String startDate, String endDate) {
        Map<String, String> result = new LinkedHashMap<>();
        try {
            Date start = java.sql.Date.valueOf(startDate);
            Date end = java.sql.Date.valueOf(endDate);
            Calendar cal = Calendar.getInstance();
            cal.setTime(start);
            while (!cal.getTime().after(end)) {
                String date = new java.text.SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
                try {
                    processFluctuationForDate(date);
                    result.put(date, "success");
                } catch (Exception e) {
                    result.put(date, "fail: " + e.getMessage());
                }
                cal.add(Calendar.DATE, 1);
            }
        } catch (Exception e) {
            result.put("all", "fail: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取展示值，fluctuation允许为null
     */
    private BigDecimal getValue(AccountInformation account, PortfolioNetValueFluctuation fluctuation) {
        if (fluctuation == null) {
            return null;
        }
        if (account.getIsCurrency() != null && account.getIsCurrency() == 1) {
            return fluctuation.getCurrentTenThousandIncome();
        } else {
            return fluctuation.getCurrentNetValue();
        }
    }

    public boolean syncFluctuationRate() {
        try {
            PortfolioFluctuationRangeService rangeService = SpringUtil.getBean(PortfolioFluctuationRangeService.class);
            List<PortfolioFluctuationRange> ranges = rangeService.list();
            List<PortfolioNetValueFluctuation> portfolioNetValueFluctuations = this.baseMapper.selectList(null);
            List<String> noValidateList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(portfolioNetValueFluctuations)) {
                for (PortfolioNetValueFluctuation fluctuation : portfolioNetValueFluctuations) {
                    BigDecimal fluctuationRate = fluctuation.getFluctuationRate();
                    String productId = fluctuation.getProductId();
                    boolean validated = rangeService.validateFluctuationRateByRangeList(fluctuationRate, productId, ranges);
                    if (!validated) {
                        noValidateList.add(fluctuation.getId());
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(noValidateList)) {
                LambdaUpdateWrapper<PortfolioNetValueFluctuation> wrapper = new LambdaUpdateWrapper<>();
                wrapper.set(PortfolioNetValueFluctuation::getAbnormalFluctuation, 1);
                wrapper.in(PortfolioNetValueFluctuation::getId, noValidateList);
                this.baseMapper.update(wrapper);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
