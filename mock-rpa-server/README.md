# Mock RPA Server

这是一个用于测试的Mock RPA服务器，模拟真实RPA服务器的接口行为。

## 功能特性

- 模拟RPA服务器的 `/CallFunc.aom` 接口
- 支持获取流程ID（GetFlowIDByFullPath）
- 支持启动流程（StartFlow）
- 支持查询流程执行状态（GetFlowExecedState）
- 前9次查询返回"正在执行"，第10次返回"执行成功"

## 支持的流程

1. **T0T1估值表**
   - 路径: `安联资管_财务运营部\安联资管_财务运营部_估值系统T0T1估值表\财务运营部_估值系统估值系统T0T1估值表`
   - FlowID: `56CA11C83CEF473A87E25EA991C623CE`

2. **会计科目余额表**
   - 路径: `安联资管_财务运营部\安联资管_财务运营部_估值系统会计科目余额表\财务运营部_估值系统会计科目余额表`
   - FlowID: `4A4F1BD4533F4745AA522407DEF4914A`

3. **深证通估值表移动**
   - 路径: `安联资管_财务运营部\安联资管_财务运营部_深证通估值表移动\财务运营部_深证通估值表移动`
   - FlowID: `7B8E2FD5644A4C96B9633518AEF5825F`

## 快速开始

### 1. 编译项目

```bash
cd mock-rpa-server
mvn clean package
```

### 2. 启动服务

```bash
java -jar target/mock-rpa-server-1.0.0.jar
```

或者使用Maven启动：

```bash
mvn spring-boot:run
```

### 3. 验证服务

访问健康检查接口：
```bash
curl http://localhost:8080/health
```

## API接口

### 主要接口

**POST /CallFunc.aom**

模拟RPA服务器的主要接口，支持以下方法：

#### 1. 获取流程ID

请求示例：
```json
[
    {
        "Type": 4,
        "Name": "{9F8E5ECB-5976-4315-B8F3-43B8502B694D}",
        "Value": "TFlowDM"
    },
    {
        "Type": 4,
        "Name": "{2881E26D-62CE-4937-B4BB-8998440417C4}",
        "Value": "GetFlowIDByFullPath"
    },
    {
        "Type": 4,
        "Name": "FlowPath",
        "Value": "安联资管_财务运营部\\安联资管_财务运营部_估值系统T0T1估值表\\财务运营部_估值系统估值系统T0T1估值表"
    }
]
```

响应示例：
```json
[
    {
        "Type": 4,
        "Name": "FlowID",
        "Value": "56CA11C83CEF473A87E25EA991C623CE"
    }
]
```

#### 2. 启动流程

请求示例：
```json
[
    {
        "Type": 4,
        "Name": "{9F8E5ECB-5976-4315-B8F3-43B8502B694D}",
        "Value": "TFlowDM"
    },
    {
        "Type": 4,
        "Name": "{2881E26D-62CE-4937-B4BB-8998440417C4}",
        "Value": "StartFlow"
    },
    {
        "Type": 4,
        "Name": "FlowID",
        "Value": "56CA11C83CEF473A87E25EA991C623CE"
    }
]
```

响应示例：
```json
[
    {
        "Type": 4,
        "Name": "ExecID",
        "Value": "DE269F18D0E340F7BCE194E750C09CEE"
    }
]
```

#### 3. 查询流程执行状态

请求示例：
```json
[
    {
        "Type": 4,
        "Name": "{9F8E5ECB-5976-4315-B8F3-43B8502B694D}",
        "Value": "TUserDM"
    },
    {
        "Type": 4,
        "Name": "{2881E26D-62CE-4937-B4BB-8998440417C4}",
        "Value": "GetFlowExecedState"
    },
    {
        "Type": 4,
        "Name": "IDs",
        "Value": "56CA11C83CEF473A87E25EA991C623CE"
    },
    {
        "Type": 4,
        "Name": "DBDate",
        "Value": "20241201"
    }
]
```

### 辅助接口

- **GET /health** - 健康检查
- **GET /info** - 服务信息

## 配置说明

可以通过修改 `application.yml` 来调整配置：

```yaml
mock:
  rpa:
    execution:
      executing-count: 9  # 前几次返回正在执行状态
      success-message: "执行成功"  # 成功消息
```

## 注意事项

1. 这是一个Mock服务器，仅用于测试目的
2. ExecID使用UUID生成，每次启动流程都会生成新的ID
3. 执行状态查询基于内存计数，服务重启后会重置
4. 默认端口为8080，可通过 `server.port` 配置修改

## 日志

服务启动后会在控制台输出详细的请求和响应日志，便于调试。
